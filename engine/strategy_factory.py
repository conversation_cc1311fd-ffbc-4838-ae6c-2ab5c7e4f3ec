import os
import importlib
import importlib.util
from typing import Dict, List, Tuple, Optional, Any, Type
from pathlib import Path
from py_strategy_api.strategy_api import StrategyApi
from utils.flog import flogger
from common.strategy_instance_manager import StrategyInstanceManager

class StrategyInstanceIdHelper:
    """生成策略实例 ID，遵循 C++ 规则"""
    _strategy_mark = 0
    _mark_file = "./strategy_mark.log"

    @classmethod
    def create_strategy_instance_id(cls, node_id: int = 1) -> int:
        """创建 ID，格式：(engine_id << 16) | strategy_mark"""
        cls._get_strategy_mark()
        return (node_id << 16) | cls._strategy_mark

    @classmethod
    def _get_strategy_mark(cls) -> None:
        """从文件中获取/增加策略标记"""
        if cls._strategy_mark == 0 and os.path.exists(cls._mark_file):
            with open(cls._mark_file, "r") as f:
                cls._strategy_mark = int(f.read() or 0)

        cls._strategy_mark += 1
        with open(cls._mark_file, "w") as f:
            f.write(str(cls._strategy_mark))


class StrategyFactory:
    """策略工厂，负责加载和创建策略"""

    def __init__(self, engine, strategy_manager: StrategyInstanceManager, node_id: int = 1 ):
        flogger.info("strategy factory start init")
        self.engine = engine
        # strategy_instance_id -> strategy_instance
        # self.strategy_instances: Dict[int, StrategyInstance] = {}
        # strategy_name -> strategy_class
        self.classes: Dict[str, Type[StrategyApi]] = {}
        # strategy_name -> status (ACTIVE/INACTIVE)
        self.status: Dict[str, str] = {}
        self.strategy_dir = Path(__file__).parent.joinpath("strategies")
        self.node_id = node_id
        self.strategy_manager = strategy_manager
        flogger.debug("strategy factory configuration",
                      node_id=self.node_id, strategy_dir=str(self.strategy_dir))
        flogger.info("strategy factory end init")

    #todo wrapper instance
    def create_strategy_instance(self, strategy_name: str, strategy_instance_name: str, trading_account_id: int, instance_params: Dict[str, Any] = None) -> int:
        """创建策略实例"""
        flogger.info("creating strategy instance",
                     strategy_name=strategy_name, params=instance_params)
        if strategy_name not in self.classes:
            flogger.error("strategy not found", strategy_name=strategy_name)
            return None
        if self.status.get(strategy_name) != "ACTIVE":
            flogger.error("strategy not active", strategy_name=strategy_name,
                          status=self.status.get(strategy_name))
            return None
        try:
            # 创建策略实例
            strategy_class = self.classes[strategy_name]
            strategy_instance = strategy_class(self.engine)

            # 设置参数
            if instance_params:
                flogger.debug("setting strategy parameters",
                              strategy_name=strategy_name, strategy_instance_name=strategy_instance_name, param=instance_params)
                for key, value in instance_params.items():
                    if hasattr(strategy_instance, key):
                        setattr(strategy_instance, key, value)
                        flogger.debug(
                            "set strategy instance parameter", strategy_name=strategy_name, strategy_instance_name=strategy_instance_name, param_name=key, param_value=value)
                    else:
                        flogger.warning(
                            "parameter not found in strategy instance", strategy_instance_name=strategy_instance_name, param_name=key, strategy_name=strategy_name)

            # 生成策略ID
            strategy_instance_id = StrategyInstanceIdHelper.create_strategy_instance_id(
                self.node_id)

            self.strategy_manager.add_strategy_instance_wrapper(strategy_instance_id, strategy_name, strategy_instance_name, trading_account_id, strategy_instance)

            flogger.info("strategy instance created",
                         strategy_name=strategy_name, strategy_instance_name=strategy_instance_name, strategy_id=strategy_instance_id)
            return strategy_instance_id
        except Exception as e:
            flogger.error("strategy instance creation failed",
                          strategy_name=strategy_name, strategy_instance_name=strategy_instance_name, error=str(e))
            return None

    def load_strategy_from_file(self, file_path: str) -> bool:
        """从文件加载策略"""
        flogger.info("loading strategy from file", file_path=file_path)
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                flogger.error("strategy file not found", file_path=file_path)
                return False

            # 获取文件名（不含扩展名）
            file_name = os.path.basename(file_path)
            module_name = os.path.splitext(file_name)[0]
            flogger.debug("extracted module name",
                          file_name=file_name, module_name=module_name)

            # 使用spec_from_file_location来动态加载模块
            spec = importlib.util.spec_from_file_location(module_name, file_path)
            if spec is None or spec.loader is None:
                flogger.error("failed to create module spec", file_path=file_path)
                return False

            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            flogger.debug("module loaded successfully", module_name=module_name)

            # 查找策略类
            for name in dir(module):
                value = getattr(module, name)
                if (isinstance(value, type) and
                    issubclass(value, StrategyApi) and
                        value != StrategyApi):
                    self.classes[name] = value
                    self.status[name] = "ACTIVE"
                    flogger.info("strategy loaded",
                                 strategy_name=name, file_path=file_path)
                    return True
            flogger.error("no strategy class found in file",
                          file_path=file_path, classes_checked=len(dir(module)))
            return False
        except Exception as e:
            flogger.error("strategy loaded failed",
                          file_path=file_path, error=str(e))
            return False

    def load_all(self, dir_path: str = None) -> None:
        """加载所有策略"""
        path = Path(dir_path) if dir_path else self.strategy_dir
        flogger.info("loading all strategies from directory",
                     directory=str(path))

        # 检查目录是否存在
        if not path.exists():
            flogger.warning("strategy directory does not exist",
                            directory=str(path))
            return

        # 加载所有Python文件
        python_files = list(path.glob("*.py"))
        flogger.debug("found python files", file_count=len(python_files))
        loaded_count = 0
        for file_path in python_files:
            if file_path.name.startswith("__"):
                flogger.debug("skipping special file",
                              file_name=file_path.name)
                continue

            flogger.debug("loading strategy file", file_path=str(file_path))
            if self.load_strategy_from_file(str(file_path)):
                loaded_count += 1

        flogger.info("strategy loading completed", total_files=len(
            python_files), loaded_count=loaded_count)

