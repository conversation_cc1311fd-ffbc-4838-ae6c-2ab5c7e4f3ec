import unittest
from unittest.mock import Mock, MagicMock, patch
from engine.strategy_caller import StrategyCaller
from common.strategy_instance_manager import StrategyInstanceManager
from common.mdb import MemoryDatabase
from common.front_api import FrontApi
from py_strategy_api.fb_models import FbOrderEntity, FbCancelOrderEntity, FbInstrumentParamValueEntity, FbCustomParamValueEntity
from py_strategy_api.strategy_api import StrategyApi
from utils.id_generator import IdGenerator


class MockStrategy(StrategyApi):
    """测试用的模拟策略"""
    strategy_verison = "1.0.0"
    parameters = []
    
    def __init__(self, engine):
        super().__init__(engine)


class TestStrategyCaller(unittest.TestCase):
    """StrategyCaller测试用例"""

    def setUp(self):
        """测试前准备"""
        # 重置单例
        StrategyInstanceManager._instance = None
        IdGenerator._instance = None
        
        self.node_id = 1
        self.mdb = MemoryDatabase()
        self.strategy_manager = StrategyInstanceManager.get_instance(self.node_id, self.mdb)
        self.mock_front_api = Mock(spec=FrontApi)
        self.mock_engine = Mock()
        
        # 创建策略调用器
        self.caller = StrategyCaller(self.mock_front_api, self.strategy_manager, self.node_id)
        
        # 添加测试策略实例
        self.strategy_id = 12345
        self.trading_account_id = 1001
        strategy_api = MockStrategy(self.mock_engine)
        self.strategy_manager.add_strategy_instance_wrapper(
            self.strategy_id, "MockStrategy", "test_instance", 
            self.trading_account_id, strategy_api
        )

    def tearDown(self):
        """测试后清理"""
        StrategyInstanceManager._instance = None
        IdGenerator._instance = None

    @patch('utils.id_generator.IdGenerator.get_order_id')
    def test_send_order_success(self, mock_get_order_id):
        """测试成功发送订单"""
        # 模拟订单ID生成
        expected_order_id = 98765
        mock_get_order_id.return_value = expected_order_id
        
        # 创建测试订单
        order = FbOrderEntity(
            instrument_id="000001",
            direction=1,  # 买入
            offset_flag=0,  # 开仓
            price=10.5,
            volume=100
        )
        
        # 发送订单
        result_order_id = self.caller.send_order(self.strategy_id, order)
        
        # 验证结果
        self.assertEqual(result_order_id, expected_order_id)
        
        # 验证调用
        mock_get_order_id.assert_called_once_with(self.node_id)
        self.mock_front_api.create_order.assert_called_once()
        
        # 验证调用参数
        call_args = self.mock_front_api.create_order.call_args
        sent_order = call_args[0][0]
        request_id = call_args[0][1]
        
        self.assertEqual(sent_order.order_id, expected_order_id)
        self.assertEqual(sent_order.trading_account_id, self.trading_account_id)
        self.assertEqual(sent_order.instrument_id, "000001")
        self.assertEqual(request_id, 1)  # 第一个请求ID

    @patch('utils.id_generator.IdGenerator.get_order_id')
    def test_send_order_exception(self, mock_get_order_id):
        """测试发送订单异常"""
        # 模拟异常
        mock_get_order_id.side_effect = Exception("ID generation failed")
        
        order = FbOrderEntity(
            instrument_id="000001",
            direction=1,
            offset_flag=0,
            price=10.5,
            volume=100
        )
        
        # 发送订单
        result_order_id = self.caller.send_order(self.strategy_id, order)
        
        # 应该返回-1表示失败
        self.assertEqual(result_order_id, -1)

    def test_cancel_order_success(self):
        """测试成功撤销订单"""
        # 创建撤单请求
        cancel_order = FbCancelOrderEntity(
            instrument_id="000001",
            order_id=98765
        )
        
        # 撤销订单
        result = self.caller.cancel_order(self.strategy_id, cancel_order)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证调用
        self.mock_front_api.cancel_order.assert_called_once()
        
        # 验证调用参数
        call_args = self.mock_front_api.cancel_order.call_args
        sent_order = call_args[0][0]
        request_id = call_args[0][1]
        
        self.assertEqual(sent_order.order_id, 98765)
        self.assertEqual(sent_order.trading_account_id, self.trading_account_id)
        self.assertEqual(request_id, 1)

    def test_cancel_order_exception(self):
        """测试撤销订单异常"""
        # 模拟异常
        self.mock_front_api.cancel_order.side_effect = Exception("Cancel failed")
        
        cancel_order = FbCancelOrderEntity(
            instrument_id="000001",
            order_id=98765
        )
        
        # 撤销订单
        result = self.caller.cancel_order(self.strategy_id, cancel_order)
        
        # 应该返回False表示失败
        self.assertFalse(result)

    def test_modify_instrument_param_value_success(self):
        """测试成功修改合约参数"""
        # 创建参数修改请求
        param_value = FbInstrumentParamValueEntity(
            instrument_id="000001",
            param_key="max_position",
            param_value="1000"
        )
        
        # 修改参数
        result = self.caller.modify_instrument_param_value(self.strategy_id, param_value)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证调用
        self.mock_front_api.modify_instrument_param_value.assert_called_once()
        
        # 验证调用参数
        call_args = self.mock_front_api.modify_instrument_param_value.call_args
        sent_param = call_args[0][0]
        request_id = call_args[0][1]
        
        self.assertEqual(sent_param.instrument_id, "000001")
        self.assertEqual(sent_param.param_key, "max_position")
        self.assertEqual(sent_param.param_value, "1000")
        self.assertEqual(sent_param.trading_account_id, self.trading_account_id)
        self.assertEqual(request_id, 1)

    def test_modify_instrument_param_value_exception(self):
        """测试修改合约参数异常"""
        # 模拟异常
        self.mock_front_api.modify_instrument_param_value.side_effect = Exception("Modify failed")
        
        param_value = FbInstrumentParamValueEntity(
            instrument_id="000001",
            param_key="max_position",
            param_value="1000"
        )
        
        # 修改参数
        result = self.caller.modify_instrument_param_value(self.strategy_id, param_value)
        
        # 应该返回False表示失败
        self.assertFalse(result)

    def test_modify_custom_param_value_success(self):
        """测试成功修改自定义参数"""
        # 创建自定义参数修改请求
        custom_param = FbCustomParamValueEntity(
            custom_id=123,
            param_key="risk_limit",
            param_value="50000"
        )
        
        # 修改参数
        result = self.caller.modify_custom_param_value(self.strategy_id, custom_param)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证调用
        self.mock_front_api.modify_custom_param_value.assert_called_once()
        
        # 验证调用参数
        call_args = self.mock_front_api.modify_custom_param_value.call_args
        sent_param = call_args[0][0]
        request_id = call_args[0][1]
        
        self.assertEqual(sent_param.custom_id, 123)
        self.assertEqual(sent_param.param_key, "risk_limit")
        self.assertEqual(sent_param.param_value, "50000")
        self.assertEqual(sent_param.trading_account_id, self.trading_account_id)
        self.assertEqual(request_id, 1)

    def test_modify_custom_param_value_exception(self):
        """测试修改自定义参数异常"""
        # 模拟异常
        self.mock_front_api.modify_custom_param_value.side_effect = Exception("Modify failed")
        
        custom_param = FbCustomParamValueEntity(
            custom_id=123,
            param_key="risk_limit",
            param_value="50000"
        )
        
        # 修改参数
        result = self.caller.modify_custom_param_value(self.strategy_id, custom_param)
        
        # 应该返回False表示失败
        self.assertFalse(result)

    def test_request_id_increment(self):
        """测试请求ID递增"""
        # 创建多个请求
        order1 = FbOrderEntity(instrument_id="000001", direction=1, offset_flag=0, price=10.0, volume=100)
        order2 = FbOrderEntity(instrument_id="000002", direction=2, offset_flag=1, price=20.0, volume=200)
        
        with patch('utils.id_generator.IdGenerator.get_order_id', return_value=1):
            self.caller.send_order(self.strategy_id, order1)
            self.caller.send_order(self.strategy_id, order2)
        
        # 验证请求ID递增
        calls = self.mock_front_api.create_order.call_args_list
        self.assertEqual(len(calls), 2)
        self.assertEqual(calls[0][0][1], 1)  # 第一个请求ID
        self.assertEqual(calls[1][0][1], 2)  # 第二个请求ID

    def test_nonexistent_strategy_instance(self):
        """测试不存在的策略实例"""
        nonexistent_id = 99999
        order = FbOrderEntity(instrument_id="000001", direction=1, offset_flag=0, price=10.0, volume=100)
        
        with patch('utils.id_generator.IdGenerator.get_order_id', return_value=1):
            # 这应该会在获取trading_account_id时返回None，导致异常
            result = self.caller.send_order(nonexistent_id, order)
        
        # 应该返回-1表示失败
        self.assertEqual(result, -1)


if __name__ == '__main__':
    unittest.main()
