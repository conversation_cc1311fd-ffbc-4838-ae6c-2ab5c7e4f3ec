import unittest
import os
import tempfile
from unittest.mock import Mock, MagicMock, patch
from pathlib import Path
from engine.strategy_factory import StrategyFactory, StrategyInstanceIdHelper
from common.strategy_instance_manager import StrategyInstanceManager
from common.mdb import MemoryDatabase
from py_strategy_api.strategy_api import StrategyApi


class TestStrategy(StrategyApi):
    """测试用的策略类"""
    strategy_verison = "1.0.0"
    parameters = ["test_param", "another_param"]
    
    def __init__(self, engine):
        super().__init__(engine)
        self.test_param = 100
        self.another_param = "default"


class TestStrategyFactory(unittest.TestCase):
    """StrategyFactory测试用例"""

    def setUp(self):
        """测试前准备"""
        # 重置单例
        StrategyInstanceManager._instance = None
        self.node_id = 1
        self.mdb = MemoryDatabase()
        self.strategy_manager = StrategyInstanceManager.get_instance(self.node_id, self.mdb)
        self.mock_engine = Mock()
        self.factory = StrategyFactory(self.mock_engine, self.strategy_manager, self.node_id)

    def tearDown(self):
        """测试后清理"""
        StrategyInstanceManager._instance = None
        # 清理策略标记文件
        if os.path.exists(StrategyInstanceIdHelper._mark_file):
            os.remove(StrategyInstanceIdHelper._mark_file)

    def test_strategy_instance_id_generation(self):
        """测试策略实例ID生成"""
        node_id = 5
        
        # 第一次生成
        id1 = StrategyInstanceIdHelper.create_strategy_instance_id(node_id)
        # 第二次生成
        id2 = StrategyInstanceIdHelper.create_strategy_instance_id(node_id)
        
        # ID应该不同
        self.assertNotEqual(id1, id2)
        
        # 验证格式：(node_id << 16) | strategy_mark
        self.assertEqual(id1 >> 16, node_id)
        self.assertEqual(id2 >> 16, node_id)
        
        # 策略标记应该递增
        mark1 = id1 & 0xFFFF
        mark2 = id2 & 0xFFFF
        self.assertEqual(mark2, mark1 + 1)

    def test_strategy_instance_id_persistence(self):
        """测试策略实例ID持久化"""
        node_id = 3
        
        # 生成一个ID
        id1 = StrategyInstanceIdHelper.create_strategy_instance_id(node_id)
        
        # 重置类变量，模拟重启
        StrategyInstanceIdHelper._strategy_mark = 0
        
        # 再次生成ID
        id2 = StrategyInstanceIdHelper.create_strategy_instance_id(node_id)
        
        # 新ID应该基于文件中保存的标记继续递增
        mark1 = id1 & 0xFFFF
        mark2 = id2 & 0xFFFF
        self.assertEqual(mark2, mark1 + 1)

    def test_create_strategy_instance_success(self):
        """测试成功创建策略实例"""
        # 手动注册策略类
        self.factory.classes["TestStrategy"] = TestStrategy
        self.factory.status["TestStrategy"] = "ACTIVE"
        
        strategy_name = "TestStrategy"
        strategy_instance_name = "test_instance"
        trading_account_id = 1001
        params = {"test_param": 200, "another_param": "custom"}
        
        # 创建策略实例
        strategy_id = self.factory.create_strategy_instance(
            strategy_name, strategy_instance_name, trading_account_id, params
        )
        
        # 验证结果
        self.assertIsNotNone(strategy_id)
        self.assertTrue(self.strategy_manager.check_strategy_instance_exists(strategy_id))
        
        # 验证参数设置
        wrapper = self.strategy_manager.get_strategy_instance_wrapper(strategy_id)
        self.assertEqual(wrapper.parameters["test_param"], 200)
        self.assertEqual(wrapper.parameters["another_param"], "custom")

    def test_create_strategy_instance_not_found(self):
        """测试创建不存在的策略实例"""
        strategy_name = "NonExistentStrategy"
        strategy_instance_name = "test_instance"
        trading_account_id = 1001
        
        # 尝试创建不存在的策略
        strategy_id = self.factory.create_strategy_instance(
            strategy_name, strategy_instance_name, trading_account_id
        )
        
        # 应该返回None
        self.assertIsNone(strategy_id)

    def test_create_strategy_instance_inactive(self):
        """测试创建非活跃状态的策略实例"""
        # 注册策略但设为非活跃
        self.factory.classes["TestStrategy"] = TestStrategy
        self.factory.status["TestStrategy"] = "INACTIVE"
        
        strategy_name = "TestStrategy"
        strategy_instance_name = "test_instance"
        trading_account_id = 1001
        
        # 尝试创建非活跃策略
        strategy_id = self.factory.create_strategy_instance(
            strategy_name, strategy_instance_name, trading_account_id
        )
        
        # 应该返回None
        self.assertIsNone(strategy_id)

    def test_create_strategy_instance_invalid_params(self):
        """测试使用无效参数创建策略实例"""
        # 注册策略
        self.factory.classes["TestStrategy"] = TestStrategy
        self.factory.status["TestStrategy"] = "ACTIVE"
        
        strategy_name = "TestStrategy"
        strategy_instance_name = "test_instance"
        trading_account_id = 1001
        # 包含无效参数
        params = {"test_param": 200, "invalid_param": "should_be_ignored"}
        
        # 创建策略实例
        strategy_id = self.factory.create_strategy_instance(
            strategy_name, strategy_instance_name, trading_account_id, params
        )
        
        # 应该成功创建，但无效参数被忽略
        self.assertIsNotNone(strategy_id)
        wrapper = self.strategy_manager.get_strategy_instance_wrapper(strategy_id)
        self.assertEqual(wrapper.parameters["test_param"], 200)
        self.assertNotIn("invalid_param", wrapper.parameters)

    def test_load_strategy_from_file(self):
        """测试从文件加载策略"""
        # 创建临时策略文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('''
from py_strategy_api.strategy_api import StrategyApi

class TempTestStrategy(StrategyApi):
    strategy_verison = "1.0.0"
    parameters = ["temp_param"]
    
    def __init__(self, engine):
        super().__init__(engine)
        self.temp_param = 50
''')
            temp_file = f.name
        
        try:
            # 加载策略
            result = self.factory.load_strategy_from_file(temp_file)
            
            # 验证加载成功
            self.assertTrue(result)
            self.assertIn("TempTestStrategy", self.factory.classes)
            self.assertEqual(self.factory.status["TempTestStrategy"], "ACTIVE")
            
        finally:
            # 清理临时文件
            os.unlink(temp_file)

    def test_load_strategy_from_invalid_file(self):
        """测试从无效文件加载策略"""
        # 尝试加载不存在的文件
        result = self.factory.load_strategy_from_file("nonexistent_file.py")
        self.assertFalse(result)
        
        # 创建无效的Python文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('invalid python syntax !!!')
            temp_file = f.name
        
        try:
            result = self.factory.load_strategy_from_file(temp_file)
            self.assertFalse(result)
        finally:
            os.unlink(temp_file)

    def test_load_strategy_no_strategy_class(self):
        """测试加载没有策略类的文件"""
        # 创建没有策略类的Python文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('''
# 这个文件没有策略类
def some_function():
    pass

class NotAStrategy:
    pass
''')
            temp_file = f.name
        
        try:
            result = self.factory.load_strategy_from_file(temp_file)
            self.assertFalse(result)
        finally:
            os.unlink(temp_file)

    def test_load_all_strategies(self):
        """测试加载所有策略"""
        # 创建临时目录和策略文件
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建有效的策略文件
            strategy_file1 = os.path.join(temp_dir, "strategy1.py")
            with open(strategy_file1, 'w') as f:
                f.write('''
from py_strategy_api.strategy_api import StrategyApi

class Strategy1(StrategyApi):
    strategy_verison = "1.0.0"
    parameters = []
    
    def __init__(self, engine):
        super().__init__(engine)
''')
            
            # 创建另一个有效的策略文件
            strategy_file2 = os.path.join(temp_dir, "strategy2.py")
            with open(strategy_file2, 'w') as f:
                f.write('''
from py_strategy_api.strategy_api import StrategyApi

class Strategy2(StrategyApi):
    strategy_verison = "1.0.0"
    parameters = []
    
    def __init__(self, engine):
        super().__init__(engine)
''')
            
            # 创建无效文件（应该被跳过）
            invalid_file = os.path.join(temp_dir, "__init__.py")
            with open(invalid_file, 'w') as f:
                f.write("# init file")
            
            # 加载所有策略
            self.factory.load_all(temp_dir)
            
            # 验证结果
            self.assertIn("Strategy1", self.factory.classes)
            self.assertIn("Strategy2", self.factory.classes)
            self.assertEqual(self.factory.status["Strategy1"], "ACTIVE")
            self.assertEqual(self.factory.status["Strategy2"], "ACTIVE")

    def test_load_all_nonexistent_directory(self):
        """测试加载不存在的目录"""
        # 这应该不会抛出异常，只是记录警告
        self.factory.load_all("/nonexistent/directory")
        
        # 策略字典应该保持为空
        self.assertEqual(len(self.factory.classes), 0)


if __name__ == '__main__':
    unittest.main()
