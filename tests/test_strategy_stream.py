import unittest
from unittest.mock import Mock, MagicMock, patch
from engine.strategy_stream import StrategyStream
from common.event import EventEngine, Event
from common.mdb import MemoryDatabase
from common.strategy_instance_manager import StrategyInstanceManager
from common.constants import *
from py_strategy_api.strategy_api import <PERSON><PERSON><PERSON>
from py_strategy_api.fb_enum import StrategyStateEnum


class MockStrategy(StrategyApi):
    """测试用的模拟策略"""
    strategy_verison = "1.0.0"
    parameters = []
    
    def __init__(self, engine):
        super().__init__(engine)
        # 记录回调调用
        self.callback_calls = []
    
    def on_market_data(self, data):
        self.callback_calls.append(('market_data', data))
    
    def on_order(self, data):
        self.callback_calls.append(('order', data))
    
    def on_trade(self, data):
        self.callback_calls.append(('trade', data))
    
    def on_position(self, data):
        self.callback_calls.append(('position', data))
    
    def on_instrument_param_value(self, data):
        self.callback_calls.append(('instrument_param_value', data))
    
    def on_custom_param_value(self, data):
        self.callback_calls.append(('custom_param_value', data))
    
    def on_security_instrument(self, data):
        self.callback_calls.append(('security_instrument', data))
    
    def on_future_instrument(self, data):
        self.callback_calls.append(('future_instrument', data))
    
    def on_option_instrument(self, data):
        self.callback_calls.append(('option_instrument', data))
    
    def on_trading_account(self, data):
        self.callback_calls.append(('trading_account', data))
    
    def on_user(self, data):
        self.callback_calls.append(('user', data))


class TestStrategyStream(unittest.TestCase):
    """StrategyStream测试用例"""

    def setUp(self):
        """测试前准备"""
        # 重置单例
        StrategyInstanceManager._instance = None
        
        self.event_engine = EventEngine()
        self.mdb = MemoryDatabase()
        self.strategy_manager = StrategyInstanceManager.get_instance(1, self.mdb)
        self.mock_engine = Mock()
        
        # 创建策略流
        self.stream = StrategyStream(self.event_engine, self.mdb, self.strategy_manager)
        
        # 添加测试策略实例
        self.strategy_id = 12345
        self.strategy_api = MockStrategy(self.mock_engine)
        self.strategy_manager.add_strategy_instance_wrapper(
            self.strategy_id, "MockStrategy", "test_instance", 1001, self.strategy_api
        )
        
        # 设置策略为运行状态
        self.strategy_manager.update_strategy_status(
            self.strategy_id, StrategyStateEnum.STRATEGY_RUNNING_STAT.value
        )

    def tearDown(self):
        """测试后清理"""
        StrategyInstanceManager._instance = None
        self.event_engine.stop()

    def test_event_handlers_registration(self):
        """测试事件处理器注册"""
        # 验证事件处理器已注册
        handlers = self.event_engine._handlers
        
        # 需要回调的事件
        callback_events = [
            EVENT_MARKET_DATA, EVENT_ORDER, EVENT_TRADE, EVENT_POSITION,
            EVENT_INSTRUMENT_PARAM_VALUE, EVENT_CUSTOM_PARAM_VALUE,
            EVENT_SECURITY_INSTRUMENT, EVENT_FUTURE_INSTRUMENT, EVENT_OPTION_INSTRUMENT,
            EVENT_TRADING_ACCOUNT
        ]
        
        for event_type in callback_events:
            self.assertIn(event_type, handlers)
            self.assertEqual(handlers[event_type][0], self.stream._process_with_callback)
        
        # 不需要回调的事件
        self.assertIn(EVENT_USER, handlers)
        self.assertEqual(handlers[EVENT_USER][0], self.stream._process_without_callback)

    def test_process_with_callback(self):
        """测试带回调的事件处理"""
        # 创建测试事件
        test_data = {"instrument_id": "000001", "price": 10.5}
        event = Event(EVENT_MARKET_DATA, test_data)
        
        # 模拟MDB更新
        with patch.object(self.mdb, 'update_record') as mock_update:
            # 处理事件
            self.stream._process_with_callback(event)
            
            # 验证MDB更新被调用
            mock_update.assert_called_once_with(EVENT_MARKET_DATA, test_data)
        
        # 验证策略回调被调用
        self.assertEqual(len(self.strategy_api.callback_calls), 1)
        self.assertEqual(self.strategy_api.callback_calls[0], ('market_data', test_data))

    def test_process_without_callback(self):
        """测试不带回调的事件处理"""
        # 创建测试事件
        test_data = {"user_id": "user001", "name": "test_user"}
        event = Event(EVENT_USER, test_data)
        
        # 模拟MDB更新
        with patch.object(self.mdb, 'update_record') as mock_update:
            # 处理事件
            self.stream._process_without_callback(event)
            
            # 验证MDB更新被调用
            mock_update.assert_called_once_with(EVENT_USER, test_data)
        
        # 验证策略回调没有被调用
        self.assertEqual(len(self.strategy_api.callback_calls), 0)

    def test_dispatch_to_strategy_instances_market_data(self):
        """测试市场数据分发到策略实例"""
        test_data = {"instrument_id": "000001", "price": 10.5}
        
        self.stream._dispatch_to_strategy_instances(EVENT_MARKET_DATA, test_data)
        
        # 验证策略回调
        self.assertEqual(len(self.strategy_api.callback_calls), 1)
        self.assertEqual(self.strategy_api.callback_calls[0], ('market_data', test_data))

    def test_dispatch_to_strategy_instances_order(self):
        """测试订单事件分发到策略实例"""
        test_data = {"order_id": 12345, "status": "filled"}
        
        self.stream._dispatch_to_strategy_instances(EVENT_ORDER, test_data)
        
        # 验证策略回调
        self.assertEqual(len(self.strategy_api.callback_calls), 1)
        self.assertEqual(self.strategy_api.callback_calls[0], ('order', test_data))

    def test_dispatch_to_strategy_instances_trade(self):
        """测试成交事件分发到策略实例"""
        test_data = {"trade_id": 67890, "volume": 100}
        
        self.stream._dispatch_to_strategy_instances(EVENT_TRADE, test_data)
        
        # 验证策略回调
        self.assertEqual(len(self.strategy_api.callback_calls), 1)
        self.assertEqual(self.strategy_api.callback_calls[0], ('trade', test_data))

    def test_dispatch_to_strategy_instances_unknown_event(self):
        """测试未知事件类型分发"""
        test_data = {"unknown": "data"}
        
        self.stream._dispatch_to_strategy_instances("UNKNOWN_EVENT", test_data)
        
        # 验证策略回调没有被调用
        self.assertEqual(len(self.strategy_api.callback_calls), 0)

    def test_dispatch_only_to_running_strategies(self):
        """测试只分发给运行中的策略"""
        # 添加另一个策略实例，但不设为运行状态
        strategy_id2 = 12346
        strategy_api2 = MockStrategy(self.mock_engine)
        self.strategy_manager.add_strategy_instance_wrapper(
            strategy_id2, "MockStrategy2", "test_instance2", 1002, strategy_api2
        )
        # 保持初始状态（非运行状态）
        
        test_data = {"instrument_id": "000001", "price": 10.5}
        
        self.stream._dispatch_to_strategy_instances(EVENT_MARKET_DATA, test_data)
        
        # 验证只有运行中的策略收到回调
        self.assertEqual(len(self.strategy_api.callback_calls), 1)  # 运行中的策略
        self.assertEqual(len(strategy_api2.callback_calls), 0)      # 非运行状态的策略

    def test_dispatch_to_multiple_running_strategies(self):
        """测试分发给多个运行中的策略"""
        # 添加另一个运行中的策略实例
        strategy_id2 = 12346
        strategy_api2 = MockStrategy(self.mock_engine)
        self.strategy_manager.add_strategy_instance_wrapper(
            strategy_id2, "MockStrategy2", "test_instance2", 1002, strategy_api2
        )
        # 设置为运行状态
        self.strategy_manager.update_strategy_status(
            strategy_id2, StrategyStateEnum.STRATEGY_RUNNING_STAT.value
        )
        
        test_data = {"instrument_id": "000001", "price": 10.5}
        
        self.stream._dispatch_to_strategy_instances(EVENT_MARKET_DATA, test_data)
        
        # 验证两个策略都收到回调
        self.assertEqual(len(self.strategy_api.callback_calls), 1)
        self.assertEqual(len(strategy_api2.callback_calls), 1)
        self.assertEqual(self.strategy_api.callback_calls[0], ('market_data', test_data))
        self.assertEqual(strategy_api2.callback_calls[0], ('market_data', test_data))

    def test_process_custom_handler(self):
        """测试自定义处理器"""
        # 创建自定义处理函数
        custom_calls = []
        def custom_handler(data):
            custom_calls.append(data)
        
        test_data = {"custom": "data"}
        event = Event(EVENT_MARKET_DATA, test_data)
        
        # 模拟MDB更新
        with patch.object(self.mdb, 'update_record') as mock_update:
            # 处理事件
            self.stream._process_custom(event, custom_handler)
            
            # 验证MDB更新被调用
            mock_update.assert_called_once_with(EVENT_MARKET_DATA, test_data)
        
        # 验证自定义处理函数被调用
        self.assertEqual(len(custom_calls), 1)
        self.assertEqual(custom_calls[0], test_data)
        
        # 验证策略回调也被调用
        self.assertEqual(len(self.strategy_api.callback_calls), 1)
        self.assertEqual(self.strategy_api.callback_calls[0], ('market_data', test_data))

    def test_all_event_types_mapping(self):
        """测试所有事件类型的映射"""
        event_mappings = [
            (EVENT_MARKET_DATA, 'market_data'),
            (EVENT_ORDER, 'order'),
            (EVENT_TRADE, 'trade'),
            (EVENT_POSITION, 'position'),
            (EVENT_INSTRUMENT_PARAM_VALUE, 'instrument_param_value'),
            (EVENT_CUSTOM_PARAM_VALUE, 'custom_param_value'),
            (EVENT_SECURITY_INSTRUMENT, 'security_instrument'),
            (EVENT_FUTURE_INSTRUMENT, 'future_instrument'),
            (EVENT_OPTION_INSTRUMENT, 'option_instrument'),
            (EVENT_TRADING_ACCOUNT, 'trading_account'),
        ]
        
        for event_type, expected_callback in event_mappings:
            # 清空之前的回调记录
            self.strategy_api.callback_calls.clear()
            
            test_data = {"test": f"data_for_{event_type}"}
            self.stream._dispatch_to_strategy_instances(event_type, test_data)
            
            # 验证正确的回调被调用
            self.assertEqual(len(self.strategy_api.callback_calls), 1)
            self.assertEqual(self.strategy_api.callback_calls[0], (expected_callback, test_data))


if __name__ == '__main__':
    unittest.main()
