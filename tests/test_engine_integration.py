import unittest
import threading
import time
from unittest.mock import Mock, MagicMock, patch
from engine.strategy_factory import StrategyFactory
from engine.strategy_caller import StrategyCaller
from engine.strategy_stream import StrategyStream
from common.strategy_instance_manager import StrategyInstanceManager
from common.event import EventEngine, Event
from common.mdb import MemoryDatabase
from common.front_api import FrontApi
from common.constants import *
from py_strategy_api.strategy_api import StrategyApi
from py_strategy_api.fb_enum import StrategyStateEnum
from py_strategy_api.fb_models import FbOrderEntity


class IntegrationTestStrategy(StrategyApi):
    """集成测试用的策略"""
    strategy_verison = "1.0.0"
    parameters = ["test_param", "risk_limit"]
    
    def __init__(self, engine):
        super().__init__(engine)
        self.test_param = 100
        self.risk_limit = 10000
        self.received_events = []
        self.orders_sent = []
    
    def on_market_data(self, data):
        self.received_events.append(('market_data', data))
        # 模拟策略逻辑：价格变化时发送订单
        if data.get('price', 0) > 10:
            order = FbOrderEntity(
                instrument_id=data.get('instrument_id', '000001'),
                direction=1,
                offset_flag=0,
                price=data.get('price', 0) - 0.1,
                volume=100
            )
            order_id = self.send_order(order)
            self.orders_sent.append(order_id)
    
    def on_order(self, data):
        self.received_events.append(('order', data))
    
    def on_trade(self, data):
        self.received_events.append(('trade', data))


class TestEngineIntegration(unittest.TestCase):
    """Engine模块集成测试"""

    def setUp(self):
        """测试前准备"""
        # 重置所有单例
        StrategyInstanceManager._instance = None
        
        self.node_id = 1
        self.event_engine = EventEngine()
        self.mdb = MemoryDatabase()
        self.strategy_manager = StrategyInstanceManager.get_instance(self.node_id, self.mdb)
        self.mock_front_api = Mock(spec=FrontApi)
        self.mock_engine = Mock()
        
        # 创建各个组件
        self.factory = StrategyFactory(self.mock_engine, self.strategy_manager, self.node_id)
        self.caller = StrategyCaller(self.mock_front_api, self.strategy_manager, self.node_id)
        self.stream = StrategyStream(self.event_engine, self.mdb, self.strategy_manager)
        
        # 注册测试策略
        self.factory.classes["IntegrationTestStrategy"] = IntegrationTestStrategy
        self.factory.status["IntegrationTestStrategy"] = "ACTIVE"

    def tearDown(self):
        """测试后清理"""
        StrategyInstanceManager._instance = None
        self.event_engine.stop()

    def test_complete_strategy_lifecycle(self):
        """测试完整的策略生命周期"""
        # 1. 创建策略实例
        strategy_id = self.factory.create_strategy_instance(
            "IntegrationTestStrategy", 
            "integration_test", 
            1001,
            {"test_param": 200, "risk_limit": 20000}
        )
        
        self.assertIsNotNone(strategy_id)
        self.assertTrue(self.strategy_manager.check_strategy_instance_exists(strategy_id))
        
        # 2. 验证策略参数设置
        wrapper = self.strategy_manager.get_strategy_instance_wrapper(strategy_id)
        self.assertEqual(wrapper.parameters["test_param"], 200)
        self.assertEqual(wrapper.parameters["risk_limit"], 20000)
        
        # 3. 启动策略
        result = self.strategy_manager.update_strategy_status(
            strategy_id, StrategyStateEnum.STRATEGY_RUNNING_STAT.value
        )
        self.assertTrue(result)
        
        # 4. 发送市场数据事件
        market_data = {
            "instrument_id": "000001",
            "price": 15.5,
            "volume": 1000,
            "timestamp": "2023-01-01 10:00:00"
        }
        
        with patch.object(self.mdb, 'update_record'):
            event = Event(EVENT_MARKET_DATA, market_data)
            self.stream._process_with_callback(event)
        
        # 5. 验证策略收到事件
        strategy_api = self.strategy_manager.get_strategy_instance_api(strategy_id)
        self.assertEqual(len(strategy_api.received_events), 1)
        self.assertEqual(strategy_api.received_events[0], ('market_data', market_data))
        
        # 6. 停止策略
        result = self.strategy_manager.update_strategy_status(
            strategy_id, StrategyStateEnum.STRATEGY_STOP_STAT.value
        )
        self.assertTrue(result)
        
        # 7. 删除策略实例
        result = self.strategy_manager.remove_strategy_instance_wrapper(strategy_id)
        self.assertTrue(result)
        self.assertFalse(self.strategy_manager.check_strategy_instance_exists(strategy_id))

    @patch('utils.id_generator.IdGenerator.get_order_id')
    def test_strategy_order_flow(self, mock_get_order_id):
        """测试策略下单流程"""
        # 模拟订单ID生成
        mock_get_order_id.return_value = 98765
        
        # 1. 创建并启动策略
        strategy_id = self.factory.create_strategy_instance(
            "IntegrationTestStrategy", "order_test", 1001
        )
        self.strategy_manager.update_strategy_status(
            strategy_id, StrategyStateEnum.STRATEGY_RUNNING_STAT.value
        )
        
        # 2. 获取策略API并设置caller
        strategy_api = self.strategy_manager.get_strategy_instance_api(strategy_id)
        strategy_api.set_strategy_caller(self.caller)
        
        # 3. 发送触发下单的市场数据
        market_data = {"instrument_id": "000001", "price": 12.0}
        
        with patch.object(self.mdb, 'update_record'):
            event = Event(EVENT_MARKET_DATA, market_data)
            self.stream._process_with_callback(event)
        
        # 4. 验证策略发送了订单
        self.assertEqual(len(strategy_api.orders_sent), 1)
        self.assertEqual(strategy_api.orders_sent[0], 98765)
        
        # 5. 验证Front API被调用
        self.mock_front_api.create_order.assert_called_once()

    def test_multiple_strategies_event_distribution(self):
        """测试多策略事件分发"""
        # 1. 创建多个策略实例
        strategy_ids = []
        for i in range(3):
            strategy_id = self.factory.create_strategy_instance(
                "IntegrationTestStrategy", f"multi_test_{i}", 1001 + i
            )
            strategy_ids.append(strategy_id)
            self.strategy_manager.update_strategy_status(
                strategy_id, StrategyStateEnum.STRATEGY_RUNNING_STAT.value
            )
        
        # 2. 发送事件
        market_data = {"instrument_id": "000001", "price": 8.0}  # 不触发下单的价格
        
        with patch.object(self.mdb, 'update_record'):
            event = Event(EVENT_MARKET_DATA, market_data)
            self.stream._process_with_callback(event)
        
        # 3. 验证所有策略都收到事件
        for strategy_id in strategy_ids:
            strategy_api = self.strategy_manager.get_strategy_instance_api(strategy_id)
            self.assertEqual(len(strategy_api.received_events), 1)
            self.assertEqual(strategy_api.received_events[0], ('market_data', market_data))

    def test_strategy_parameter_update_during_runtime(self):
        """测试运行时策略参数更新"""
        # 1. 创建并启动策略
        strategy_id = self.factory.create_strategy_instance(
            "IntegrationTestStrategy", "param_test", 1001
        )
        self.strategy_manager.update_strategy_status(
            strategy_id, StrategyStateEnum.STRATEGY_RUNNING_STAT.value
        )
        
        # 2. 更新参数
        result = self.strategy_manager.update_strategy_parameter(
            strategy_id, "test_param", 500
        )
        self.assertTrue(result)
        
        # 3. 验证参数更新
        strategy_api = self.strategy_manager.get_strategy_instance_api(strategy_id)
        self.assertEqual(strategy_api.test_param, 500)
        
        wrapper = self.strategy_manager.get_strategy_instance_wrapper(strategy_id)
        self.assertEqual(wrapper.parameters["test_param"], 500)

    def test_concurrent_strategy_operations(self):
        """测试并发策略操作"""
        results = []
        
        def create_strategies():
            for i in range(5):
                strategy_id = self.factory.create_strategy_instance(
                    "IntegrationTestStrategy", 
                    f"concurrent_{threading.current_thread().ident}_{i}", 
                    1001
                )
                results.append(('create', strategy_id))
                if strategy_id:
                    # 启动策略
                    self.strategy_manager.update_strategy_status(
                        strategy_id, StrategyStateEnum.STRATEGY_RUNNING_STAT.value
                    )
                time.sleep(0.001)
        
        def send_events():
            time.sleep(0.01)  # 等待策略创建
            for i in range(10):
                market_data = {"instrument_id": f"00000{i%3}", "price": 5.0 + i}
                with patch.object(self.mdb, 'update_record'):
                    event = Event(EVENT_MARKET_DATA, market_data)
                    self.stream._process_with_callback(event)
                time.sleep(0.001)
        
        # 创建并启动线程
        threads = []
        for _ in range(2):
            t1 = threading.Thread(target=create_strategies)
            t2 = threading.Thread(target=send_events)
            threads.extend([t1, t2])
        
        for t in threads:
            t.start()
        
        for t in threads:
            t.join()
        
        # 验证结果
        create_results = [r for r in results if r[0] == 'create']
        successful_creates = [r for r in create_results if r[1] is not None]
        
        # 应该有成功创建的策略
        self.assertGreater(len(successful_creates), 0)
        
        # 验证策略管理器状态一致
        total_strategies = self.strategy_manager.get_strategy_instance_count()
        self.assertEqual(total_strategies, len(successful_creates))

    def test_error_handling_in_strategy_callback(self):
        """测试策略回调中的错误处理"""
        # 创建会抛出异常的策略
        class ErrorStrategy(StrategyApi):
            strategy_verison = "1.0.0"
            parameters = []
            
            def __init__(self, engine):
                super().__init__(engine)
            
            def on_market_data(self, data):
                raise Exception("Strategy callback error")
        
        # 注册错误策略
        self.factory.classes["ErrorStrategy"] = ErrorStrategy
        self.factory.status["ErrorStrategy"] = "ACTIVE"
        
        # 创建策略实例
        strategy_id = self.factory.create_strategy_instance(
            "ErrorStrategy", "error_test", 1001
        )
        self.strategy_manager.update_strategy_status(
            strategy_id, StrategyStateEnum.STRATEGY_RUNNING_STAT.value
        )
        
        # 发送事件（应该不会导致整个系统崩溃）
        market_data = {"instrument_id": "000001", "price": 10.0}
        
        with patch.object(self.mdb, 'update_record'):
            # 这不应该抛出异常
            try:
                event = Event(EVENT_MARKET_DATA, market_data)
                self.stream._process_with_callback(event)
            except Exception as e:
                self.fail(f"Strategy callback error should be handled: {e}")


if __name__ == '__main__':
    unittest.main()
