import unittest
import threading
import time
from unittest.mock import Mock, MagicMock, patch
from common.strategy_instance_manager import StrategyInstanceManager, StrategyInstanceWrapper
from common.mdb import MemoryDatabase
from py_strategy_api.strategy_api import StrategyApi
from py_strategy_api.fb_enum import StrategyStateEnum


class MockStrategy(StrategyApi):
    """测试用的模拟策略"""
    strategy_verison = "1.0.0"
    parameters = ["param1", "param2"]
    
    def __init__(self, engine):
        super().__init__(engine)
        self.param1 = 10
        self.param2 = "test"


class TestStrategyInstanceManager(unittest.TestCase):
    """StrategyInstanceManager测试用例"""

    def setUp(self):
        """测试前准备"""
        # 重置单例
        StrategyInstanceManager._instance = None
        self.node_id = 1
        self.mdb = MemoryDatabase()
        self.manager = StrategyInstanceManager.get_instance(self.node_id, self.mdb)
        self.mock_engine = Mock()

    def tearDown(self):
        """测试后清理"""
        StrategyInstanceManager._instance = None

    def test_singleton_pattern(self):
        """测试单例模式"""
        manager1 = StrategyInstanceManager.get_instance(1, self.mdb)
        manager2 = StrategyInstanceManager.get_instance(2, self.mdb)  # 不同参数
        
        # 应该返回同一个实例
        self.assertIs(manager1, manager2)
        self.assertEqual(manager1.node_id, 1)  # 保持第一次创建时的参数

    def test_add_strategy_instance_wrapper(self):
        """测试添加策略实例包装器"""
        strategy_id = 12345
        strategy_name = "MockStrategy"
        strategy_instance_name = "test_instance"
        trading_account_id = 1001
        strategy_api = MockStrategy(self.mock_engine)

        # 添加策略实例
        result = self.manager.add_strategy_instance_wrapper(
            strategy_id, strategy_name, strategy_instance_name, 
            trading_account_id, strategy_api
        )

        self.assertTrue(result)
        self.assertTrue(self.manager.check_strategy_instance_exists(strategy_id))
        self.assertEqual(self.manager.get_strategy_name(strategy_id), strategy_name)
        self.assertEqual(self.manager.get_strategy_instance_name(strategy_id), strategy_instance_name)
        self.assertEqual(self.manager.get_strategy_instance_trading_account_id(strategy_id), trading_account_id)
        self.assertEqual(self.manager.get_strategy_instance_status(strategy_id), 
                        StrategyStateEnum.STRATEGY_INIT_STAT.value)

    def test_remove_strategy_instance_wrapper(self):
        """测试移除策略实例包装器"""
        strategy_id = 12345
        strategy_api = MockStrategy(self.mock_engine)
        
        # 先添加
        self.manager.add_strategy_instance_wrapper(
            strategy_id, "MockStrategy", "test_instance", 1001, strategy_api
        )
        
        # 再移除
        result = self.manager.remove_strategy_instance_wrapper(strategy_id)
        
        self.assertTrue(result)
        self.assertFalse(self.manager.check_strategy_instance_exists(strategy_id))

    def test_update_strategy_status(self):
        """测试更新策略状态"""
        strategy_id = 12345
        strategy_api = MockStrategy(self.mock_engine)
        
        # 添加策略实例
        self.manager.add_strategy_instance_wrapper(
            strategy_id, "MockStrategy", "test_instance", 1001, strategy_api
        )
        
        # 更新状态
        new_status = StrategyStateEnum.STRATEGY_RUNNING_STAT.value
        result = self.manager.update_strategy_status(strategy_id, new_status)
        
        self.assertTrue(result)
        self.assertEqual(self.manager.get_strategy_instance_status(strategy_id), new_status)

    def test_update_strategy_parameter(self):
        """测试更新策略参数"""
        strategy_id = 12345
        strategy_api = MockStrategy(self.mock_engine)
        
        # 添加策略实例
        self.manager.add_strategy_instance_wrapper(
            strategy_id, "MockStrategy", "test_instance", 1001, strategy_api
        )
        
        # 更新参数
        result = self.manager.update_strategy_parameter(strategy_id, "param1", 20)
        
        self.assertTrue(result)
        wrapper = self.manager.get_strategy_instance_wrapper(strategy_id)
        self.assertEqual(wrapper.parameters["param1"], 20)
        self.assertEqual(strategy_api.param1, 20)

    def test_get_strategy_instance_parameters(self):
        """测试获取策略参数"""
        strategy_id = 12345
        strategy_api = MockStrategy(self.mock_engine)
        
        # 添加策略实例
        self.manager.add_strategy_instance_wrapper(
            strategy_id, "MockStrategy", "test_instance", 1001, strategy_api
        )
        
        # 获取参数
        parameters = self.manager.get_strategy_instance_parameters(strategy_id)
        
        self.assertEqual(len(parameters), 2)
        param_names = [p.param_name for p in parameters]
        self.assertIn("param1", param_names)
        self.assertIn("param2", param_names)

    def test_get_strategies_by_status(self):
        """测试根据状态获取策略列表"""
        # 添加多个策略实例
        for i in range(3):
            strategy_id = 12345 + i
            strategy_api = MockStrategy(self.mock_engine)
            self.manager.add_strategy_instance_wrapper(
                strategy_id, f"MockStrategy{i}", f"test_instance{i}", 1001, strategy_api
            )
        
        # 更新其中一个的状态
        self.manager.update_strategy_status(12345, StrategyStateEnum.STRATEGY_RUNNING_STAT.value)
        
        # 获取不同状态的策略
        init_strategies = self.manager.get_strategies_by_status(StrategyStateEnum.STRATEGY_INIT_STAT.value)
        running_strategies = self.manager.get_strategies_by_status(StrategyStateEnum.STRATEGY_RUNNING_STAT.value)
        
        self.assertEqual(len(init_strategies), 2)
        self.assertEqual(len(running_strategies), 1)
        self.assertIn(12345, running_strategies)

    def test_get_strategies_by_account(self):
        """测试根据交易账户获取策略列表"""
        # 添加不同账户的策略实例
        accounts = [1001, 1002, 1001]
        for i, account in enumerate(accounts):
            strategy_id = 12345 + i
            strategy_api = MockStrategy(self.mock_engine)
            self.manager.add_strategy_instance_wrapper(
                strategy_id, f"MockStrategy{i}", f"test_instance{i}", account, strategy_api
            )
        
        # 获取特定账户的策略
        account_1001_strategies = self.manager.get_strategies_by_account(1001)
        account_1002_strategies = self.manager.get_strategies_by_account(1002)
        
        self.assertEqual(len(account_1001_strategies), 2)
        self.assertEqual(len(account_1002_strategies), 1)

    def test_thread_safety_add_remove(self):
        """测试线程安全 - 并发添加和删除"""
        results = []
        
        def add_strategies():
            for i in range(10):
                strategy_id = 10000 + threading.current_thread().ident + i
                strategy_api = MockStrategy(self.mock_engine)
                result = self.manager.add_strategy_instance_wrapper(
                    strategy_id, f"Strategy{i}", f"instance{i}", 1001, strategy_api
                )
                results.append(('add', strategy_id, result))
                time.sleep(0.001)  # 模拟处理时间
        
        def remove_strategies():
            time.sleep(0.01)  # 等待一些策略被添加
            for i in range(5):
                strategy_id = 10000 + threading.current_thread().ident + i
                result = self.manager.remove_strategy_instance_wrapper(strategy_id)
                results.append(('remove', strategy_id, result))
                time.sleep(0.001)
        
        # 创建多个线程
        threads = []
        for _ in range(3):
            t1 = threading.Thread(target=add_strategies)
            t2 = threading.Thread(target=remove_strategies)
            threads.extend([t1, t2])
        
        # 启动所有线程
        for t in threads:
            t.start()
        
        # 等待所有线程完成
        for t in threads:
            t.join()
        
        # 验证结果
        add_results = [r for r in results if r[0] == 'add']
        self.assertTrue(all(r[2] for r in add_results))  # 所有添加操作都应该成功

    def test_thread_safety_status_update(self):
        """测试线程安全 - 并发状态更新"""
        strategy_id = 12345
        strategy_api = MockStrategy(self.mock_engine)
        
        # 添加策略实例
        self.manager.add_strategy_instance_wrapper(
            strategy_id, "MockStrategy", "test_instance", 1001, strategy_api
        )
        
        results = []
        
        def update_status():
            for i in range(10):
                status = StrategyStateEnum.STRATEGY_RUNNING_STAT.value if i % 2 == 0 else StrategyStateEnum.STRATEGY_PAUSE_STAT.value
                result = self.manager.update_strategy_status(strategy_id, status)
                results.append(result)
                time.sleep(0.001)
        
        # 创建多个线程同时更新状态
        threads = []
        for _ in range(3):
            t = threading.Thread(target=update_status)
            threads.append(t)
        
        # 启动所有线程
        for t in threads:
            t.start()
        
        # 等待所有线程完成
        for t in threads:
            t.join()
        
        # 验证结果 - 所有更新都应该成功
        self.assertTrue(all(results))
        
        # 最终状态应该是有效的
        final_status = self.manager.get_strategy_instance_status(strategy_id)
        valid_statuses = [StrategyStateEnum.STRATEGY_RUNNING_STAT.value, StrategyStateEnum.STRATEGY_PAUSE_STAT.value]
        self.assertIn(final_status, valid_statuses)


if __name__ == '__main__':
    unittest.main()
